import 'package:isar/isar.dart';

part 'user_model.g.dart';

@collection
class UserModel {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  late String? supabaseUserId; // UUID from auth.users

  @Index(unique: true)
  late String email;

  late String? hashedPassword; // For offline authentication
  late String? accessToken;
  late String? refreshToken;

  late String fullName;
  late String? phoneNumber;

  @enumerated
  late UserRole role;

  late int? restaurantId; // Links to restaurant.id
  late bool isActive;
  late bool isLoggedIn; // Track offline login state
  late bool isDeleted;

  late DateTime createdAt;
  late DateTime? lastLoginAt;
  late DateTime? lastSyncAt;

  UserModel({
    this.supabaseUserId,
    this.email = '',
    this.hashedPassword,
    this.accessToken,
    this.refreshToken,
    this.fullName = '',
    this.phoneNumber,
    this.role = UserRole.waiter,
    this.restaurantId,
    this.isActive = true,
    this.isLoggedIn = false,
    this.isDeleted = false,
    this.lastLoginAt,
    this.lastSyncAt,
  }) : createdAt = DateTime.now();

  bool get hasValidTokens => accessToken != null && refreshToken != null;

  bool get needsSync =>
      lastSyncAt == null || DateTime.now().difference(lastSyncAt!).inHours > 1;

  // Convert from Supabase profile data
  factory UserModel.fromSupabase(
    Map<String, dynamic> profile,
    String supabaseUserId,
  ) {
    final user = UserModel(
      supabaseUserId: supabaseUserId,
      email: profile['email'] ?? '',
      fullName: profile['full_name'] ?? '',
      phoneNumber: profile['phone_number'],
      role: UserRole.values.firstWhere(
        (r) => r.name == profile['role'],
        orElse: () => UserRole.waiter,
      ),
      restaurantId: profile['restaurant_id'],
      isActive: profile['is_active'] ?? true,
      isDeleted: profile['is_deleted'] ?? false,
    );

    if (profile['created_at'] != null) {
      user.createdAt = DateTime.parse(profile['created_at']);
    }

    return user;
  }
}

enum UserRole { admin, owner, biller, waiter }
