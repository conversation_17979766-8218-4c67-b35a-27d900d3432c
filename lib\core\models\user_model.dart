import 'package:isar/isar.dart';

part 'user_model.g.dart';

@collection
class UserModel {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  late String email;

  late String? hashedPassword; // Store hashed password for offline auth
  late String? supabaseUserId;
  late String? accessToken;
  late String? refreshToken;
  
  late String firstName;
  late String lastName;
  late String? phone;
  late String? avatarUrl;
  
  @enumerated
  late UserRole role;
  
  late String? restaurantId;
  late bool isActive;
  late bool isLoggedIn; // Track offline login state
  
  late DateTime createdAt;
  late DateTime updatedAt;
  late DateTime? lastLoginAt;
  late DateTime? lastSyncAt;

  UserModel({
    this.email = '',
    this.hashedPassword,
    this.supabaseUserId,
    this.accessToken,
    this.refreshToken,
    this.firstName = '',
    this.lastName = '',
    this.phone,
    this.avatarUrl,
    this.role = UserRole.waiter,
    this.restaurantId,
    this.isActive = true,
    this.isLoggedIn = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.lastLoginAt,
    this.lastSyncAt,
  }) : 
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  String get fullName => '$firstName $lastName'.trim();
  
  bool get hasValidTokens => accessToken != null && refreshToken != null;
  
  bool get needsSync => lastSyncAt == null || 
    DateTime.now().difference(lastSyncAt!).inHours > 1;
}

enum UserRole {
  owner,
  admin,
  manager,
  cashier,
  waiter,
  kitchen,
}
