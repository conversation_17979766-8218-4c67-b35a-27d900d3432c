Leveraging the guidelines in `@/.kilocode/rules/flutter-rules.md`, `@/.kilocode/rules/project-overview.md`, and `@/.kilocode/rules/UI-UX.md`, develop the foundational Flutter UI for a billing interface, ensuring the design is fully informed by and compatible with the database schema. Check the Database !!, Firstly our priority is to make a fully offline storage /  optimised app for full offline supoort thats why we are using isar, so build the 

<!-- D:\Dev\Flutter\Project\billing\.kilocode\rules create a .md file to explain how to use the snack bar isnide the @d:\Dev\Flutter\Project\billing/.kilocode\rules/  -->


Leveraging the guidelines in

@d:\Dev\Flutter\Project\billing/.kilocode\rules\flutter-rules.md @d:\Dev\Flutter\Project\billing/.kilocode\rules\project-overview.md @d:\Dev\Flutter\Project\billing/.kilocode\rules\UI-UX.md @d:\Dev\Flutter\Project\billing/.kilocode\rules\cendra-alert-usage.md 

develop the foundational Flutter UI for a billing interface, ensuring the design is fully informed by and compatible with the database schema. Check the Database !!, Firstly our priority is to make a fully offline storage /  optimised app for full offline supoort thats why we are using isar, 

THIS APP PRIMARY FOCUS IS USED AS AN WINDOWS APP

the flow is this 

1, user logs in, then save that user cred in offline , by deafullt the app showed auto loggin regardless if its ocnnected to internet or not, then even if somehow we log out while offline, we should be anble to log in wihout inter net 

2. download the config according to the user , (this is why checking and planing the DB is important) , every resturant has its own config, so donwload it and cache it / store it  offline and applyt the themeing and logos and all the other infos fromt the config, show the the tables

lets just implement this now