import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/database/database_service.dart';
import '../../../core/models/user_model.dart';

class OfflineAuthRepository {
  final SupabaseClient _client = Supabase.instance.client;
  final DatabaseService _db = DatabaseService.instance;

  // Hash password for offline storage
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Online authentication with offline caching
  Future<UserModel> signInWithPassword(String email, String password) async {
    try {
      // Try online authentication first
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Fetch user profile
        final profileResponse = await _client
            .from('profiles')
            .select()
            .eq('user_id', response.user!.id)
            .single();

        // Create user model from Supabase data
        final user = UserModel.fromSupabase(profileResponse, response.user!.id);
        user.hashedPassword = _hashPassword(password);
        user.accessToken = response.session?.accessToken;
        user.refreshToken = response.session?.refreshToken;
        user.isLoggedIn = true;
        user.lastLoginAt = DateTime.now();

        // Save to local database
        await _db.saveUser(user);
        await _db.loginUser(email);

        return user;
      } else {
        throw 'Authentication failed';
      }
    } on AuthException catch (e) {
      // If online auth fails, try offline authentication
      return await _signInOffline(email, password);
    } catch (e) {
      // If online auth fails, try offline authentication
      return await _signInOffline(email, password);
    }
  }

  // Offline authentication
  Future<UserModel> _signInOffline(String email, String password) async {
    final user = await _db.getUserByEmail(email);
    
    if (user == null) {
      throw 'User not found. Please connect to internet for first login.';
    }

    if (user.hashedPassword == null) {
      throw 'Offline authentication not available. Please connect to internet.';
    }

    final hashedPassword = _hashPassword(password);
    if (user.hashedPassword != hashedPassword) {
      throw 'Invalid password';
    }

    // Update login state
    user.isLoggedIn = true;
    user.lastLoginAt = DateTime.now();
    await _db.saveUser(user);
    await _db.loginUser(email);

    return user;
  }

  // Auto-login for offline users
  Future<UserModel?> autoLogin() async {
    final currentUser = await _db.getCurrentUser();
    
    if (currentUser != null && currentUser.isLoggedIn) {
      // Check if we have valid tokens and try to refresh session
      if (currentUser.hasValidTokens) {
        try {
          await _client.auth.setSession(currentUser.refreshToken!);
          // Update last login time
          currentUser.lastLoginAt = DateTime.now();
          await _db.saveUser(currentUser);
          return currentUser;
        } catch (e) {
          // Token refresh failed, but user can still work offline
          return currentUser;
        }
      }
      return currentUser;
    }

    return null;
  }

  // Sign up (requires internet)
  Future<UserModel> signUpWithPassword(String email, String password, String fullName) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Create profile
        await _client.from('profiles').insert({
          'user_id': response.user!.id,
          'email': email,
          'full_name': fullName,
          'role': 'waiter',
          'is_active': true,
        });

        // Fetch the created profile
        final profileResponse = await _client
            .from('profiles')
            .select()
            .eq('user_id', response.user!.id)
            .single();

        // Create user model
        final user = UserModel.fromSupabase(profileResponse, response.user!.id);
        user.hashedPassword = _hashPassword(password);
        user.accessToken = response.session?.accessToken;
        user.refreshToken = response.session?.refreshToken;
        user.isLoggedIn = true;
        user.lastLoginAt = DateTime.now();

        // Save to local database
        await _db.saveUser(user);
        await _db.loginUser(email);

        return user;
      } else {
        throw 'Sign up failed';
      }
    } on AuthException catch (e) {
      throw e.message;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (e) {
      // Ignore online sign out errors
    }
    
    // Always perform offline logout
    await _db.logoutUser();
  }

  // Get current user
  Future<UserModel?> getCurrentUser() async {
    return await _db.getCurrentUser();
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final user = await getCurrentUser();
    return user != null && user.isLoggedIn;
  }

  // Stream of auth state changes
  Stream<UserModel?> get authStateChanges async* {
    // Initial state
    yield await getCurrentUser();
    
    // Listen to Supabase auth changes and update local state
    await for (final authState in _client.auth.onAuthStateChange) {
      if (authState.event == AuthChangeEvent.signedOut) {
        await _db.logoutUser();
        yield null;
      } else if (authState.event == AuthChangeEvent.signedIn && authState.session?.user != null) {
        final user = await getCurrentUser();
        if (user != null) {
          user.accessToken = authState.session?.accessToken;
          user.refreshToken = authState.session?.refreshToken;
          user.lastLoginAt = DateTime.now();
          await _db.saveUser(user);
          yield user;
        }
      }
    }
  }
}
