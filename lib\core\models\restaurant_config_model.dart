import 'package:isar/isar.dart';

part 'restaurant_config_model.g.dart';

@collection
class RestaurantConfigModel {
  Id id = Isar.autoIncrement;

  late int restaurantId; // Links to restaurant.id

  late String primaryColor;
  late String secondaryColor;
  late String accentColor;
  late String? logoUrl;

  late DateTime createdAt;
  late DateTime? lastSyncAt;

  RestaurantConfigModel({
    this.restaurantId = 0,
    this.primaryColor = '#3B82F6',
    this.secondaryColor = '#6366F1',
    this.accentColor = '#FACC15',
    this.logoUrl,
    this.lastSyncAt,
  }) : createdAt = DateTime.now();

  bool get needsSync =>
      lastSyncAt == null || DateTime.now().difference(lastSyncAt!).inHours > 1;

  // Convert from Supabase data
  factory RestaurantConfigModel.fromSupabase(Map<String, dynamic> data) {
    final config = RestaurantConfigModel(
      restaurantId: data['restaurant_id'],
      primaryColor: data['primary_color'] ?? '#3B82F6',
      secondaryColor: data['secondary_color'] ?? '#6366F1',
      accentColor: data['accent_color'] ?? '#FACC15',
      logoUrl: data['logo_url'],
    );

    if (data['created_at'] != null) {
      config.createdAt = DateTime.parse(data['created_at']);
    }

    return config;
  }
}
