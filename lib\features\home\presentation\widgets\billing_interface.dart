import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/user_model.dart';
import '../../../core/models/restaurant_config_model.dart';
import '../../../core/models/table_model.dart';
import '../../../core/models/menu_item_model.dart';
import '../../auth/presentation/providers/auth_providers.dart';
import 'table_grid_widget.dart';
import 'order_summary_widget.dart';
import 'command_bar_widget.dart';

class BillingInterface extends ConsumerStatefulWidget {
  final UserModel user;
  final Map<String, dynamic> restaurantData;

  const BillingInterface({
    super.key,
    required this.user,
    required this.restaurantData,
  });

  @override
  ConsumerState<BillingInterface> createState() => _BillingInterfaceState();
}

class _BillingInterfaceState extends ConsumerState<BillingInterface> {
  TableModel? selectedTable;
  List<OrderItem> currentOrder = [];

  @override
  Widget build(BuildContext context) {
    final config = widget.restaurantData['config'] as RestaurantConfigModel?;
    final tables = widget.restaurantData['tables'] as List<TableModel>;
    final menuItems = widget.restaurantData['menuItems'] as List<RestaurantMenuItemModel>;

    // Apply dynamic theming based on restaurant config
    final theme = _buildDynamicTheme(context, config);

    return Theme(
      data: theme,
      child: Scaffold(
        backgroundColor: theme.colorScheme.surface,
        appBar: _buildAppBar(theme, config),
        body: _buildThreeZoneLayout(theme, tables, menuItems),
      ),
    );
  }

  ThemeData _buildDynamicTheme(BuildContext context, RestaurantConfigModel? config) {
    final baseTheme = Theme.of(context);
    
    if (config == null) return baseTheme;

    // Parse colors from config
    final primaryColor = _parseColor(config.primaryColor) ?? baseTheme.colorScheme.primary;
    final secondaryColor = _parseColor(config.secondaryColor) ?? baseTheme.colorScheme.secondary;
    final accentColor = _parseColor(config.accentColor) ?? baseTheme.colorScheme.tertiary;

    // Create role-specific theme based on user role
    Color roleColor = primaryColor;
    switch (widget.user.role) {
      case UserRole.biller:
        roleColor = Colors.blue; // Cashier UI (Blue Theme)
        break;
      case UserRole.waiter:
        roleColor = Colors.green; // Waiter/Server UI (Green Theme)
        break;
      case UserRole.admin:
      case UserRole.owner:
        roleColor = Colors.red; // Manager UI (Red Theme)
        break;
    }

    return baseTheme.copyWith(
      colorScheme: baseTheme.colorScheme.copyWith(
        primary: roleColor,
        secondary: secondaryColor,
        tertiary: accentColor,
      ),
    );
  }

  Color? _parseColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return null;
    }
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme, RestaurantConfigModel? config) {
    return AppBar(
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.onPrimary,
      elevation: 2,
      title: Row(
        children: [
          if (config?.logoUrl != null) ...[
            CircleAvatar(
              radius: 16,
              backgroundImage: NetworkImage(config!.logoUrl!),
              backgroundColor: theme.colorScheme.onPrimary.withOpacity(0.1),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Cendra POS',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _getRoleDisplayName(widget.user.role),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        // Current table indicator
        if (selectedTable != null)
          Container(
            margin: const EdgeInsets.only(right: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: theme.colorScheme.onPrimary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.table_restaurant,
                  size: 16,
                  color: theme.colorScheme.onPrimary,
                ),
                const SizedBox(width: 4),
                Text(
                  selectedTable!.name,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        
        // User menu
        PopupMenuButton<String>(
          icon: CircleAvatar(
            radius: 16,
            backgroundColor: theme.colorScheme.onPrimary.withOpacity(0.1),
            child: Text(
              widget.user.fullName.isNotEmpty 
                ? widget.user.fullName[0].toUpperCase()
                : 'U',
              style: TextStyle(
                color: theme.colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          onSelected: (value) async {
            if (value == 'logout') {
              ref.read(authMessageProvider.notifier).state =
                  'You have been successfully logged out from Cendra.';
              await ref.read(offlineAuthRepositoryProvider).signOut();
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  Icon(Icons.person, color: theme.colorScheme.onSurface),
                  const SizedBox(width: 8),
                  Text(widget.user.fullName),
                ],
              ),
            ),
            const PopupMenuDivider(),
            PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: theme.colorScheme.error),
                  const SizedBox(width: 8),
                  const Text('Logout'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'Administrator';
      case UserRole.owner:
        return 'Owner';
      case UserRole.biller:
        return 'Cashier';
      case UserRole.waiter:
        return 'Waiter';
    }
  }

  Widget _buildThreeZoneLayout(ThemeData theme, List<TableModel> tables, List<RestaurantMenuItemModel> menuItems) {
    return Row(
      children: [
        // Zone 1: Action Zone (Table Selection - Left 60%)
        Expanded(
          flex: 6,
          child: Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              border: Border(
                right: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: TableGridWidget(
              tables: tables,
              selectedTable: selectedTable,
              onTableSelected: (table) {
                setState(() {
                  selectedTable = table;
                });
              },
            ),
          ),
        ),
        
        // Zone 2: Context Zone (Order Summary - Right 40%)
        Expanded(
          flex: 4,
          child: Column(
            children: [
              // Order summary
              Expanded(
                child: OrderSummaryWidget(
                  selectedTable: selectedTable,
                  currentOrder: currentOrder,
                  onOrderChanged: (order) {
                    setState(() {
                      currentOrder = order;
                    });
                  },
                ),
              ),
              
              // Zone 3: Command Bar (Bottom, Fixed Position)
              CommandBarWidget(
                selectedTable: selectedTable,
                currentOrder: currentOrder,
                onAction: (action) {
                  _handleCommandAction(action);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _handleCommandAction(String action) {
    switch (action) {
      case 'pay':
        // TODO: Implement payment flow
        break;
      case 'split':
        // TODO: Implement bill splitting
        break;
      case 'discount':
        // TODO: Implement discount application
        break;
      case 'hold':
        // TODO: Implement order holding
        break;
      case 'cancel':
        setState(() {
          currentOrder.clear();
          selectedTable = null;
        });
        break;
    }
  }
}

// Order item model for current order
class OrderItem {
  final RestaurantMenuItemModel menuItem;
  int quantity;
  double price;
  String? notes;

  OrderItem({
    required this.menuItem,
    this.quantity = 1,
    required this.price,
    this.notes,
  });

  double get total => price * quantity;
}
