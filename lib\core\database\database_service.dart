import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import '../models/user_model.dart';
import '../models/restaurant_config_model.dart';
import '../models/table_model.dart';
import '../models/menu_item_model.dart';

class DatabaseService {
  static DatabaseService? _instance;
  static Isar? _isar;

  DatabaseService._();

  static DatabaseService get instance {
    _instance ??= DatabaseService._();
    return _instance!;
  }

  static Isar get isar {
    if (_isar == null) {
      throw Exception('Database not initialized. Call DatabaseService.initialize() first.');
    }
    return _isar!;
  }

  static Future<void> initialize() async {
    if (_isar != null) return;

    final dir = await getApplicationDocumentsDirectory();
    
    _isar = await Isar.open(
      [
        UserModelSchema,
        RestaurantConfigModelSchema,
        TableModelSchema,
        MenuItemModelSchema,
        RestaurantMenuItemModelSchema,
      ],
      directory: dir.path,
      name: 'cendra_billing',
    );
  }

  static Future<void> close() async {
    await _isar?.close();
    _isar = null;
    _instance = null;
  }

  // User operations
  Future<UserModel?> getCurrentUser() async {
    return await isar.userModels
        .filter()
        .isLoggedInEqualTo(true)
        .findFirst();
  }

  Future<UserModel?> getUserByEmail(String email) async {
    return await isar.userModels
        .filter()
        .emailEqualTo(email)
        .findFirst();
  }

  Future<void> saveUser(UserModel user) async {
    await isar.writeTxn(() async {
      await isar.userModels.put(user);
    });
  }

  Future<void> logoutUser() async {
    final currentUser = await getCurrentUser();
    if (currentUser != null) {
      currentUser.isLoggedIn = false;
      await saveUser(currentUser);
    }
  }

  Future<void> loginUser(String email) async {
    await isar.writeTxn(() async {
      // Logout all users first
      final users = await isar.userModels.where().findAll();
      for (final user in users) {
        user.isLoggedIn = false;
        await isar.userModels.put(user);
      }
      
      // Login the specified user
      final user = await getUserByEmail(email);
      if (user != null) {
        user.isLoggedIn = true;
        user.lastLoginAt = DateTime.now();
        await isar.userModels.put(user);
      }
    });
  }

  // Restaurant config operations
  Future<RestaurantConfigModel?> getRestaurantConfig(int restaurantId) async {
    return await isar.restaurantConfigModels
        .filter()
        .restaurantIdEqualTo(restaurantId)
        .findFirst();
  }

  Future<void> saveRestaurantConfig(RestaurantConfigModel config) async {
    await isar.writeTxn(() async {
      await isar.restaurantConfigModels.put(config);
    });
  }

  // Table operations
  Future<List<TableModel>> getRestaurantTables(int restaurantId) async {
    return await isar.tableModels
        .filter()
        .restaurantIdEqualTo(restaurantId)
        .and()
        .isDeletedEqualTo(false)
        .findAll();
  }

  Future<void> saveTable(TableModel table) async {
    await isar.writeTxn(() async {
      await isar.tableModels.put(table);
    });
  }

  Future<void> saveTables(List<TableModel> tables) async {
    await isar.writeTxn(() async {
      await isar.tableModels.putAll(tables);
    });
  }

  // Menu operations
  Future<List<RestaurantMenuItemModel>> getRestaurantMenuItems(int restaurantId) async {
    return await isar.restaurantMenuItemModels
        .filter()
        .restaurantIdEqualTo(restaurantId)
        .and()
        .isDeletedEqualTo(false)
        .findAll();
  }

  Future<MenuItemModel?> getMenuItem(int menuItemId) async {
    return await isar.menuItemModels
        .filter()
        .menuItemIdEqualTo(menuItemId)
        .findFirst();
  }

  Future<void> saveMenuItems(List<MenuItemModel> items) async {
    await isar.writeTxn(() async {
      await isar.menuItemModels.putAll(items);
    });
  }

  Future<void> saveRestaurantMenuItems(List<RestaurantMenuItemModel> items) async {
    await isar.writeTxn(() async {
      await isar.restaurantMenuItemModels.putAll(items);
    });
  }

  // Sync operations
  Future<void> markSynced(dynamic model) async {
    model.lastSyncAt = DateTime.now();
    await isar.writeTxn(() async {
      if (model is UserModel) {
        await isar.userModels.put(model);
      } else if (model is RestaurantConfigModel) {
        await isar.restaurantConfigModels.put(model);
      } else if (model is TableModel) {
        await isar.tableModels.put(model);
      } else if (model is MenuItemModel) {
        await isar.menuItemModels.put(model);
      } else if (model is RestaurantMenuItemModel) {
        await isar.restaurantMenuItemModels.put(model);
      }
    });
  }

  // Clear all data (for testing/reset)
  Future<void> clearAllData() async {
    await isar.writeTxn(() async {
      await isar.clear();
    });
  }
}
