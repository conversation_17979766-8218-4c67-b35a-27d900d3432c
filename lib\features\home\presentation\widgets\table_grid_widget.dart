import 'package:flutter/material.dart';
import '../../../core/models/table_model.dart';

class TableGridWidget extends StatelessWidget {
  final List<TableModel> tables;
  final TableModel? selectedTable;
  final Function(TableModel) onTableSelected;

  const TableGridWidget({
    super.key,
    required this.tables,
    required this.selectedTable,
    required this.onTableSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.3),
            border: Border(
              bottom: BorderSide(
                color: theme.colorScheme.outline.withOpacity(0.2),
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.table_restaurant,
                color: theme.colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Restaurant Tables',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const Spacer(),
              _buildStatusLegend(theme),
            ],
          ),
        ),
        
        // Table grid
        Expanded(
          child: tables.isEmpty
              ? _buildEmptyState(theme)
              : _buildTableGrid(theme),
        ),
      ],
    );
  }

  Widget _buildStatusLegend(ThemeData theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildLegendItem(theme, 'Available', Colors.green),
        const SizedBox(width: 16),
        _buildLegendItem(theme, 'Occupied', Colors.red),
        const SizedBox(width: 16),
        _buildLegendItem(theme, 'Selected', theme.colorScheme.primary),
      ],
    );
  }

  Widget _buildLegendItem(ThemeData theme, String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(6),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.table_restaurant_outlined,
            size: 64,
            color: theme.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No Tables Available',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tables will appear here once configured',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableGrid(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4, // 4 tables per row for Windows desktop
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2, // Slightly wider than square
        ),
        itemCount: tables.length,
        itemBuilder: (context, index) {
          final table = tables[index];
          return _buildTableCard(theme, table);
        },
      ),
    );
  }

  Widget _buildTableCard(ThemeData theme, TableModel table) {
    final isSelected = selectedTable?.tableId == table.tableId;
    final status = table.status;
    
    Color backgroundColor;
    Color borderColor;
    Color textColor;
    IconData statusIcon;

    switch (status) {
      case TableStatus.available:
        backgroundColor = Colors.green.withOpacity(0.1);
        borderColor = Colors.green;
        textColor = Colors.green.shade700;
        statusIcon = Icons.check_circle;
        break;
      case TableStatus.occupied:
        backgroundColor = Colors.red.withOpacity(0.1);
        borderColor = Colors.red;
        textColor = Colors.red.shade700;
        statusIcon = Icons.people;
        break;
      case TableStatus.billPending:
        backgroundColor = Colors.orange.withOpacity(0.1);
        borderColor = Colors.orange;
        textColor = Colors.orange.shade700;
        statusIcon = Icons.receipt;
        break;
      case TableStatus.needsCleaning:
        backgroundColor = Colors.blue.withOpacity(0.1);
        borderColor = Colors.blue;
        textColor = Colors.blue.shade700;
        statusIcon = Icons.cleaning_services;
        break;
      case TableStatus.unavailable:
        backgroundColor = Colors.grey.withOpacity(0.1);
        borderColor = Colors.grey;
        textColor = Colors.grey.shade700;
        statusIcon = Icons.block;
        break;
    }

    if (isSelected) {
      backgroundColor = theme.colorScheme.primary.withOpacity(0.1);
      borderColor = theme.colorScheme.primary;
      textColor = theme.colorScheme.primary;
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: status == TableStatus.unavailable 
            ? null 
            : () => onTableSelected(table),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            color: backgroundColor,
            border: Border.all(
              color: borderColor,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: theme.colorScheme.primary.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Status icon
                Icon(
                  statusIcon,
                  color: textColor,
                  size: 24,
                ),
                const SizedBox(height: 8),
                
                // Table name
                Text(
                  table.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: textColor,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                
                // Capacity
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.person,
                      size: 14,
                      color: textColor.withOpacity(0.7),
                    ),
                    const SizedBox(width: 2),
                    Text(
                      '${table.capacity}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: textColor.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
                
                // Status text
                const SizedBox(height: 4),
                Text(
                  _getStatusText(status),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: textColor.withOpacity(0.8),
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getStatusText(TableStatus status) {
    switch (status) {
      case TableStatus.available:
        return 'Available';
      case TableStatus.occupied:
        return 'Occupied';
      case TableStatus.billPending:
        return 'Bill Pending';
      case TableStatus.needsCleaning:
        return 'Needs Cleaning';
      case TableStatus.unavailable:
        return 'Unavailable';
    }
  }
}
