import 'package:isar/isar.dart';

part 'table_model.g.dart';

@collection
class TableModel {
  Id id = Isar.autoIncrement;

  late int tableId; // Supabase table ID
  late int restaurantId;

  late String name;
  late int capacity;
  late bool isOccupied;
  late bool isDeleted;

  late DateTime createdAt;
  late DateTime? lastSyncAt;

  TableModel({
    this.tableId = 0,
    this.restaurantId = 0,
    this.name = '',
    this.capacity = 4,
    this.isOccupied = false,
    this.isDeleted = false,
    this.lastSyncAt,
  }) : createdAt = DateTime.now();

  bool get needsSync =>
      lastSyncAt == null || DateTime.now().difference(lastSyncAt!).inHours > 1;

  @enumerated
  TableStatus get status {
    if (isDeleted) return TableStatus.unavailable;
    if (isOccupied) return TableStatus.occupied;
    return TableStatus.available;
  }

  // Convert from Supabase data
  factory TableModel.fromSupabase(Map<String, dynamic> data) {
    final table = TableModel(
      tableId: data['id'],
      restaurantId: data['restaurant_id'],
      name: data['name'] ?? '',
      capacity: data['capacity'] ?? 4,
      isOccupied: data['is_occupied'] ?? false,
      isDeleted: data['is_deleted'] ?? false,
    );

    if (data['created_at'] != null) {
      table.createdAt = DateTime.parse(data['created_at']);
    }

    return table;
  }
}

enum TableStatus {
  available,
  occupied,
  billPending,
  needsCleaning,
  unavailable,
}
