import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/models/table_model.dart';
import 'billing_interface.dart';

class CommandBarWidget extends StatelessWidget {
  final TableModel? selectedTable;
  final List<OrderItem> currentOrder;
  final Function(String) onAction;

  const CommandBarWidget({
    super.key,
    required this.selectedTable,
    required this.currentOrder,
    required this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasOrder = currentOrder.isNotEmpty;
    final hasTable = selectedTable != null;

    return Container(
      height: 80,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5),
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.2),
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Primary actions (left side)
          Expanded(
            child: Row(
              children: [
                // Pay button (F8)
                _buildCommandButton(
                  theme,
                  label: 'Pay',
                  icon: Icons.payment,
                  shortcut: 'F8',
                  color: Colors.green,
                  enabled: hasOrder && hasTable,
                  onPressed: () => onAction('pay'),
                ),
                
                const SizedBox(width: 12),
                
                // Split Bill button (F7)
                _buildCommandButton(
                  theme,
                  label: 'Split Bill',
                  icon: Icons.call_split,
                  shortcut: 'F7',
                  color: Colors.blue,
                  enabled: hasOrder && hasTable,
                  onPressed: () => onAction('split'),
                ),
                
                const SizedBox(width: 12),
                
                // Apply Discount button (F6)
                _buildCommandButton(
                  theme,
                  label: 'Discount',
                  icon: Icons.percent,
                  shortcut: 'F6',
                  color: Colors.orange,
                  enabled: hasOrder && hasTable,
                  onPressed: () => onAction('discount'),
                ),
              ],
            ),
          ),
          
          // Secondary actions (right side)
          Row(
            children: [
              // Hold Order button (F5)
              _buildCommandButton(
                theme,
                label: 'Hold',
                icon: Icons.pause,
                shortcut: 'F5',
                color: Colors.amber,
                enabled: hasOrder && hasTable,
                onPressed: () => onAction('hold'),
              ),
              
              const SizedBox(width: 12),
              
              // Cancel Order button (F4)
              _buildCommandButton(
                theme,
                label: 'Cancel',
                icon: Icons.cancel,
                shortcut: 'F4',
                color: Colors.red,
                enabled: hasOrder || hasTable,
                onPressed: () => onAction('cancel'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCommandButton(
    ThemeData theme, {
    required String label,
    required IconData icon,
    required String shortcut,
    required Color color,
    required bool enabled,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: Shortcuts(
        shortcuts: {
          LogicalKeySet(LogicalKeyboardKey.f8): const PayIntent(),
          LogicalKeySet(LogicalKeyboardKey.f7): const SplitIntent(),
          LogicalKeySet(LogicalKeyboardKey.f6): const DiscountIntent(),
          LogicalKeySet(LogicalKeyboardKey.f5): const HoldIntent(),
          LogicalKeySet(LogicalKeyboardKey.f4): const CancelIntent(),
        },
        child: Actions(
          actions: {
            PayIntent: CallbackAction<PayIntent>(
              onInvoke: (intent) => shortcut == 'F8' && enabled ? onPressed() : null,
            ),
            SplitIntent: CallbackAction<SplitIntent>(
              onInvoke: (intent) => shortcut == 'F7' && enabled ? onPressed() : null,
            ),
            DiscountIntent: CallbackAction<DiscountIntent>(
              onInvoke: (intent) => shortcut == 'F6' && enabled ? onPressed() : null,
            ),
            HoldIntent: CallbackAction<HoldIntent>(
              onInvoke: (intent) => shortcut == 'F5' && enabled ? onPressed() : null,
            ),
            CancelIntent: CallbackAction<CancelIntent>(
              onInvoke: (intent) => shortcut == 'F4' && enabled ? onPressed() : null,
            ),
          },
          child: SizedBox(
            height: 48,
            child: ElevatedButton(
              onPressed: enabled ? onPressed : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: enabled ? color : theme.colorScheme.surfaceContainerHighest,
                foregroundColor: enabled ? Colors.white : theme.colorScheme.onSurface.withOpacity(0.5),
                elevation: enabled ? 2 : 0,
                shadowColor: color.withOpacity(0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 12),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(icon, size: 16),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          label,
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            fontSize: 11,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                    decoration: BoxDecoration(
                      color: enabled 
                          ? Colors.white.withOpacity(0.2)
                          : theme.colorScheme.onSurface.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: Text(
                      shortcut,
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontSize: 9,
                        fontWeight: FontWeight.bold,
                        color: enabled 
                            ? Colors.white.withOpacity(0.9)
                            : theme.colorScheme.onSurface.withOpacity(0.4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Intent classes for keyboard shortcuts
class PayIntent extends Intent {
  const PayIntent();
}

class SplitIntent extends Intent {
  const SplitIntent();
}

class DiscountIntent extends Intent {
  const DiscountIntent();
}

class HoldIntent extends Intent {
  const HoldIntent();
}

class CancelIntent extends Intent {
  const CancelIntent();
}
