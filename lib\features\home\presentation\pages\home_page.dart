import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seo_biling/core/models/user_model.dart';
import 'package:seo_biling/core/widgets/cendra_alert_service.dart';
import 'package:seo_biling/features/auth/presentation/providers/auth_providers.dart';
import 'package:seo_biling/features/config/presentation/providers/config_providers.dart';
import '../widgets/billing_interface.dart';

class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    final currentRestaurantData = ref.watch(currentRestaurantDataProvider);

    return authState.when(
      data: (UserModel? user) {
        if (user == null) {
          return const Scaffold(body: Center(child: Text('No user found')));
        }

        return currentRestaurantData.when(
          data: (data) {
            if (data == null) {
              return _buildNoRestaurantScreen(context, ref, user);
            }

            return BillingInterface(user: user, restaurantData: data);
          },
          loading: () => _buildLoadingScreen(context, user),
          error: (error, stack) => _buildErrorScreen(context, ref, user, error),
        );
      },
      loading: () =>
          const Scaffold(body: Center(child: CircularProgressIndicator())),
      error: (error, stack) =>
          Scaffold(body: Center(child: Text('Error: $error'))),
    );
  }

  Widget _buildLoadingScreen(BuildContext context, UserModel user) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Welcome, ${user.fullName}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading restaurant configuration...'),
            SizedBox(height: 8),
            Text(
              'This may take a moment on first login',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoRestaurantScreen(
    BuildContext context,
    WidgetRef ref,
    UserModel user,
  ) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Welcome, ${user.fullName}'),
        actions: [
          IconButton(
            onPressed: () => _logout(ref),
            icon: const Icon(Icons.logout),
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.restaurant,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            const Text(
              'No Restaurant Assigned',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Please contact your administrator to assign you to a restaurant.',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(
    BuildContext context,
    WidgetRef ref,
    UserModel user,
    Object error,
  ) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Welcome, ${user.fullName}'),
        actions: [
          IconButton(
            onPressed: () => _logout(ref),
            icon: const Icon(Icons.logout),
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to Load Restaurant Data',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // Refresh the data
                ref.invalidate(currentRestaurantDataProvider);
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _logout(WidgetRef ref) async {
    ref.read(authMessageProvider.notifier).state =
        'You have been successfully logged out from Cendra.';
    await ref.read(offlineAuthRepositoryProvider).signOut();
  }
}
