import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/database/database_service.dart';
import '../../../core/models/restaurant_config_model.dart';
import '../../../core/models/table_model.dart';
import '../../../core/models/menu_item_model.dart';

class ConfigRepository {
  final SupabaseClient _client = Supabase.instance.client;
  final DatabaseService _db = DatabaseService.instance;

  // Download and cache restaurant configuration
  Future<RestaurantConfigModel?> downloadRestaurantConfig(int restaurantId) async {
    try {
      // Fetch restaurant config from Supabase
      final configResponse = await _client
          .from('restaurant_config')
          .select()
          .eq('restaurant_id', restaurantId)
          .maybeSingle();

      if (configResponse != null) {
        final config = RestaurantConfigModel.fromSupabase(configResponse);
        config.lastSyncAt = DateTime.now();
        
        // Save to local database
        await _db.saveRestaurantConfig(config);
        
        return config;
      }
    } catch (e) {
      // If download fails, try to get from local cache
      return await getRestaurantConfigOffline(restaurantId);
    }
    
    return null;
  }

  // Get restaurant config from local cache
  Future<RestaurantConfigModel?> getRestaurantConfigOffline(int restaurantId) async {
    return await _db.getRestaurantConfig(restaurantId);
  }

  // Download and cache restaurant tables
  Future<List<TableModel>> downloadRestaurantTables(int restaurantId) async {
    try {
      // Fetch tables from Supabase
      final tablesResponse = await _client
          .from('tables')
          .select()
          .eq('restaurant_id', restaurantId)
          .eq('is_deleted', false);

      final tables = tablesResponse
          .map<TableModel>((data) => TableModel.fromSupabase(data))
          .toList();

      // Mark as synced and save to local database
      for (final table in tables) {
        table.lastSyncAt = DateTime.now();
      }
      
      await _db.saveTables(tables);
      
      return tables;
    } catch (e) {
      // If download fails, get from local cache
      return await getRestaurantTablesOffline(restaurantId);
    }
  }

  // Get restaurant tables from local cache
  Future<List<TableModel>> getRestaurantTablesOffline(int restaurantId) async {
    return await _db.getRestaurantTables(restaurantId);
  }

  // Download and cache menu items
  Future<void> downloadRestaurantMenu(int restaurantId) async {
    try {
      // Fetch restaurant menu items
      final menuResponse = await _client
          .from('restaurant_menus')
          .select('''
            id,
            restaurant_id,
            base_item_id,
            price,
            is_available,
            is_deleted,
            menu_items (
              id,
              name,
              description,
              image_url,
              estimated_time_minutes,
              is_deleted
            )
          ''')
          .eq('restaurant_id', restaurantId)
          .eq('is_deleted', false);

      // Process menu items
      final menuItems = <MenuItemModel>[];
      final restaurantMenuItems = <RestaurantMenuItemModel>[];

      for (final item in menuResponse) {
        // Extract base menu item
        final baseItem = item['menu_items'];
        if (baseItem != null) {
          final menuItem = MenuItemModel.fromSupabase(baseItem);
          menuItem.lastSyncAt = DateTime.now();
          menuItems.add(menuItem);
        }

        // Extract restaurant-specific menu item
        final restaurantMenuItem = RestaurantMenuItemModel.fromSupabase(item);
        restaurantMenuItem.lastSyncAt = DateTime.now();
        restaurantMenuItems.add(restaurantMenuItem);
      }

      // Save to local database
      await _db.saveMenuItems(menuItems);
      await _db.saveRestaurantMenuItems(restaurantMenuItems);
      
    } catch (e) {
      // If download fails, menu items will be loaded from cache
      print('Failed to download menu: $e');
    }
  }

  // Get restaurant menu items from local cache
  Future<List<RestaurantMenuItemModel>> getRestaurantMenuOffline(int restaurantId) async {
    return await _db.getRestaurantMenuItems(restaurantId);
  }

  // Get menu item details
  Future<MenuItemModel?> getMenuItemDetails(int menuItemId) async {
    return await _db.getMenuItem(menuItemId);
  }

  // Download all restaurant data (config, tables, menu)
  Future<void> downloadAllRestaurantData(int restaurantId) async {
    await Future.wait([
      downloadRestaurantConfig(restaurantId),
      downloadRestaurantTables(restaurantId),
      downloadRestaurantMenu(restaurantId),
    ]);
  }

  // Check if restaurant data needs sync
  Future<bool> needsSync(int restaurantId) async {
    final config = await getRestaurantConfigOffline(restaurantId);
    final tables = await getRestaurantTablesOffline(restaurantId);
    final menuItems = await getRestaurantMenuOffline(restaurantId);

    // Check if any data is missing or needs sync
    if (config == null || tables.isEmpty || menuItems.isEmpty) {
      return true;
    }

    // Check if data is stale (older than 1 hour)
    final now = DateTime.now();
    if (config.needsSync) return true;
    
    for (final table in tables) {
      if (table.needsSync) return true;
    }
    
    for (final item in menuItems) {
      if (item.needsSync) return true;
    }

    return false;
  }

  // Sync restaurant data if needed
  Future<void> syncIfNeeded(int restaurantId) async {
    if (await needsSync(restaurantId)) {
      await downloadAllRestaurantData(restaurantId);
    }
  }
}
