import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/user_model.dart';
import '../../../core/models/restaurant_config_model.dart';
import '../../../core/models/table_model.dart';
import '../../../core/models/menu_item_model.dart';
import '../../data/config_repository.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

final configRepositoryProvider = Provider<ConfigRepository>((ref) {
  return ConfigRepository();
});

// Restaurant configuration provider
final restaurantConfigProvider =
    FutureProvider.family<RestaurantConfigModel?, int>((
      ref,
      restaurantId,
    ) async {
      final configRepo = ref.watch(configRepositoryProvider);

      // Try to get from cache first
      var config = await configRepo.getRestaurantConfigOffline(restaurantId);

      // If not in cache or needs sync, download
      if (config == null || config.needsSync) {
        config = await configRepo.downloadRestaurantConfig(restaurantId);
      }

      return config;
    });

// Restaurant tables provider
final restaurantTablesProvider = FutureProvider.family<List<TableModel>, int>((
  ref,
  restaurantId,
) async {
  final configRepo = ref.watch(configRepositoryProvider);

  // Try to get from cache first
  var tables = await configRepo.getRestaurantTablesOffline(restaurantId);

  // If empty or needs sync, download
  if (tables.isEmpty || tables.any((t) => t.needsSync)) {
    tables = await configRepo.downloadRestaurantTables(restaurantId);
  }

  return tables;
});

// Restaurant menu provider
final restaurantMenuProvider =
    FutureProvider.family<List<RestaurantMenuItemModel>, int>((
      ref,
      restaurantId,
    ) async {
      final configRepo = ref.watch(configRepositoryProvider);

      // Try to get from cache first
      var menuItems = await configRepo.getRestaurantMenuOffline(restaurantId);

      // If empty or needs sync, download
      if (menuItems.isEmpty || menuItems.any((m) => m.needsSync)) {
        await configRepo.downloadRestaurantMenu(restaurantId);
        menuItems = await configRepo.getRestaurantMenuOffline(restaurantId);
      }

      return menuItems;
    });

// Current user's restaurant data provider
final currentRestaurantDataProvider = FutureProvider<Map<String, dynamic>?>((
  ref,
) async {
  final authState = ref.watch(authStateProvider);

  return authState.when(
    data: (user) async {
      if (user?.restaurantId != null) {
        final configRepo = ref.watch(configRepositoryProvider);

        // Download all restaurant data
        await configRepo.downloadAllRestaurantData(user!.restaurantId!);

        // Get all data
        final config = await configRepo.getRestaurantConfigOffline(
          user.restaurantId!,
        );
        final tables = await configRepo.getRestaurantTablesOffline(
          user.restaurantId!,
        );
        final menuItems = await configRepo.getRestaurantMenuOffline(
          user.restaurantId!,
        );

        return {
          'config': config,
          'tables': tables,
          'menuItems': menuItems,
          'restaurantId': user.restaurantId,
        };
      }
      return null;
    },
    loading: () => null,
    error: (_, __) => null,
  );
});

// Auto-sync provider - syncs data when needed
final autoSyncProvider = Provider<void>((ref) {
  final authState = ref.watch(authStateProvider);

  authState.whenData((user) async {
    if (user?.restaurantId != null) {
      final configRepo = ref.watch(configRepositoryProvider);
      await configRepo.syncIfNeeded(user!.restaurantId!);
    }
  });
});
