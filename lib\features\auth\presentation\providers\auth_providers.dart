import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/user_model.dart';
import '../../data/offline_auth_repository.dart';

final offlineAuthRepositoryProvider = Provider<OfflineAuthRepository>((ref) {
  return OfflineAuthRepository();
});

final authStateProvider = StreamProvider<UserModel?>((ref) {
  final authRepository = ref.watch(offlineAuthRepositoryProvider);
  return authRepository.authStateChanges;
});

final authMessageProvider = StateProvider<String?>((ref) => null);
