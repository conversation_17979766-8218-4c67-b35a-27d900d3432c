import 'package:isar/isar.dart';

part 'restaurant_model.g.dart';

@collection
class RestaurantModel {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  late String restaurantId; // Supabase restaurant ID
  
  late String name;
  late String? description;
  late String? logoUrl;
  late String? bannerUrl;
  
  late String address;
  late String? city;
  late String? state;
  late String? country;
  late String? zipCode;
  late String? phone;
  late String? email;
  late String? website;
  
  @enumerated
  late RestaurantType type;
  
  @enumerated
  late CurrencyType currency;
  
  late double taxRate;
  late double serviceChargeRate;
  
  late bool isActive;
  late bool hasMultipleOutlets;
  
  late DateTime createdAt;
  late DateTime updatedAt;
  late DateTime? lastSyncAt;

  RestaurantModel({
    this.restaurantId = '',
    this.name = '',
    this.description,
    this.logoUrl,
    this.bannerUrl,
    this.address = '',
    this.city,
    this.state,
    this.country,
    this.zipCode,
    this.phone,
    this.email,
    this.website,
    this.type = RestaurantType.casual,
    this.currency = CurrencyType.usd,
    this.taxRate = 0.0,
    this.serviceChargeRate = 0.0,
    this.isActive = true,
    this.hasMultipleOutlets = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.lastSyncAt,
  }) : 
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  bool get needsSync => lastSyncAt == null || 
    DateTime.now().difference(lastSyncAt!).inHours > 1;
}

enum RestaurantType {
  fastFood,
  casual,
  fineDining,
  cafe,
  bar,
  bakery,
  foodTruck,
}

enum CurrencyType {
  usd,
  eur,
  gbp,
  inr,
  cad,
  aud,
}
